<template>
  <div class="report">
    <header class="report-header">
      <div class="report-header-left">
        <img :src="reportName?.icon" alt="logo" />
        <h3>{{ reportName?.name }} Report</h3>
        <img @click="reportDropDownHandler" class="dropdown" :src="dropDownIcon" alt="dropdown icon" />
        <!-- v-show="this.staticPortalId?.includes(this.$store.state.userData.portal_id)" />-->
        <div v-show="showReportDropDown" class="report-dropdown">
          <button v-if="reportName?.name === 'Workflow'" @click="handleReportType('campaign')">
            <img :src="viraIcon" alt="Vira icon" />
            <span>Vira Report</span>
          </button>

          <button v-else @click="handleReportType('workflow')">
            <img :src="hubspotIcon" alt="Workflow icon" />
            <span>Workflow Report</span>
          </button>
        </div>
      </div>
    </header>
    <ReportViewer v-if="reportName?.name" :reportName="reportName.name" />
  </div>
</template>

<script>
import ReportViewer from '../vuechat/components/ReportViewer/ReportViewer.vue';
import WhatshiveLogo from '@/assets/icons/whatshive_logo.svg'
import DropDownIcon from '@/assets/icons/dropdown_icon.svg'
import HubspotIcon from '@/assets/icons/hubspot.svg'
import ViraIcon from '@/assets/icons/vira.svg'

export default {
  name: 'Report',

  components: {
    ReportViewer
  },

  data() {
    return {
      whatshiveLogo: WhatshiveLogo,
      dropDownIcon: DropDownIcon,
      hubspotIcon: HubspotIcon,
      viraIcon: ViraIcon,
      // reportType: 'vira',
      reportName: { name: 'Campaign', icon: ViraIcon },
      showReportDropDown: false,
      staticPortalId: ['7222284'],
    }
  },

  methods: {
    handleReportType(reportType) {
      this.reportName = reportType !== 'campaign' ? { name: 'Workflow', icon: HubspotIcon } : { name: 'Campaign', icon: ViraIcon };
      this.showReportDropDown = !this.showReportDropDown;
    },

    reportDropDownHandler() {
      this.showReportDropDown = !this.showReportDropDown;
    }
  },
  mounted() {
    console.log('Report mounted',this.$store.state.userData.portal_id)
  },
}
</script>
