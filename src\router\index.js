import Vue from 'vue'
import VueRouter from 'vue-router'
import Chats from '../views/Activities.vue'
import InstaChats from '../views/InstaActivity.vue'
import Account from '../views/Account.vue'
import FlowMapping from '../views/FlowMapping.vue'
import Templates from '../views/Templates.vue'
import Settings from '../views/Settings.vue'
import Campaigns from '../views/Lists.vue'
import Report from '../views/Report.vue'
import UserRoles from '../views/UserRoles.vue'
import axios, { setJwtToken } from '@/utils/api.js'
import store from '../store/index.js'

Vue.use(VueRouter)

let routes = [
  {
    path: '/',
    alias: '/activities',
    name: 'Chats', // Don't change this
    component: Chats
  },
  {
    path: '/insta',
    name: 'InstaChats',
    component: InstaChats
  },
  {
    path: '/account',
    name: 'Account',
    component: Account
  },
  {
    path: '/campaigns',
    name: 'Campaigns', // Don't change this
    component: Campaigns
  },
  {
    path: '/templates',
    name: 'Templates',
    component: Templates
  },
  {
    path: '/flow-mapping',
    name: 'FlowMapping',
    component: FlowMapping
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings
  },
  {
    path: '/report',
    name: 'Report',
    component: Report
  },
  {
    path: '/user-roles',
    name: 'UserRoles',
    component: UserRoles
  },
  {
    path: '/:catchAll(.*)*',
    redirect: { name: 'Account' }
  }
]

const router = new VueRouter({
  // mode: 'history',
  base: process.env.BASE_URL,
  routes,
  linkExactActiveClass: 'active-link'
})

let permissionsCache = []
let isAdmin = false
let userExists = false
let isUserActive

// Function to fetch permissions only once
async function fetchPermissions() {
  const urlParams = new URLSearchParams(window.location.search)
  let accountUser = urlParams.get('accountUser')
  let userId = urlParams.get('user_id')
  try {
    if (permissionsCache.length === 0) {
      const response = await axios.get(`api/users/${accountUser}?user_id=${userId}`)

      if (response.data.ok) {
        const user = response.data.user
        if (user) {
          // Store JWT token for future API calls
          if (user.token) {
            setJwtToken(user.token)
          }

          isUserActive = user.active
          store.commit('setUserExists', true)
          userExists = true
          store.dispatch('updateRoutePermissions', user)

          if (user.permissions) {
            permissionsCache = user.permissions?.map(permission => permission.replace(/\s+/g, ''))
            isAdmin = user.admin
          }
        } else {
          store.commit('setUserExists', false)
          console.warn('No user object found in the response.')
        }
      } else {
        console.error('API response indicates failure:', response.data)
      }
    } else {
      console.log('Permissions already fetched.')
    }
    const defaultPermissions = ['Support', 'Account', 'InstaChats', 'Templates']
    permissionsCache = isUserActive ? [...new Set([...permissionsCache, ...defaultPermissions])] : defaultPermissions

    return permissionsCache
  } catch (error) {
    store.commit('setUserExists', false)
    console.error('An error occurred while fetching permissions:', error.message)
    return []
  }
}

// Add beforeEach navigation guard
router.beforeEach(async (to, from, next) => {
  const allowedRoutes = await fetchPermissions() // Fetch cached permissions

  if (to.path === '/user-roles' && !userExists) {
    return next({ name: 'Account' })
  }

  // Allow access if the user is an admin, has permission, or doesn't exist
  if (isAdmin || allowedRoutes.includes(to.name) || !userExists) {
    return next()
  }

  // Redirect to 'Account' if none of the conditions are met
  next({ name: 'Account' })
})

export default router
