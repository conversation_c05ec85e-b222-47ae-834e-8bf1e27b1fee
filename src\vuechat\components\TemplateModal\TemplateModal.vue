<template>
  <div class="template-modal">
    <div @click="closeModal" class="modal-overlay"></div>
    <div class="modal-dialog modal-md">
      <div class="modal-content">
        <div class="modal-header justify-content-between">
          <h5 class="modal-title">Send template message</h5>
          <button type="button" class="btn" @click="closeModal">
            <img :src="closeIcon" alt="" srcset="" />
          </button>
        </div>
        <hr />
        <form @submit.prevent="submitHandler">
          <div class="modal-body">
            <p><span class="fw-bolder">Template Name: </span>{{ selectedTemplate.name }}</p>
            <p><span class="fw-bolder"> Language Code: </span>{{ selectedTemplate.language }}</p>
            <p style="white-space: pre-wrap">
              <span class="fw-bolder">Template Content: </span>{{ selectedTemplate.body }}
            </p>
            <div class="mt-5" v-if="selectedTemplate">
              <template v-if="selectedTemplate.hasHeaderParam">
                <div class="form-group" :key="selectedTemplate.id">
                  <div class="d-flex justify-content-between align-items-center">
                    <label class="form-label">Header Param Text</label>
                    <div class="dropdown">
                      <button
                        @click.prevent="tokenModalHandler('header_text_1')"
                        class="btn tokenButton dropdown-toggle"
                        type="button"
                        aria-expanded="false"
                      >
                        Contact Tokens
                      </button>
                      <ul class="token-dropdown" v-if="activeTokenModal === `header_text_1`">
                        <li>
                          <input
                            @keyup="searchProperties($event)"
                            @keydown.enter.prevent
                            type="text"
                            class="form-control search-input"
                            placeholder="Search property name"
                          />
                        </li>
                        <div class="dropdown-data">
                          <li
                            v-for="property in properties"
                            :key="property.name"
                            :data-name="property.label"
                            class="tokenItem"
                          >
                            <button
                              @click="addToken(property.name, 'header_text_1')"
                              :title="property.name"
                              class="dropdown-item"
                              type="button"
                            >
                              {{ property.label }}
                            </button>
                          </li>
                          <p class="no-data-found">Contact token not found</p>
                        </div>
                      </ul>
                    </div>
                  </div>

                  <input
                    name="header_text_1"
                    v-model="templateFields['header_text_1']"
                    @keydown.enter.prevent
                    type="text"
                    class="form-control"
                    placeholder="Enter value"
                    required
                  />
                </div>
              </template>

              <!-- Placeholder template -->
              <template v-if="selectedTemplate.params !== 0">
                <div class="form-group" v-for="index in selectedTemplate.params" :key="selectedTemplate.id + index">
                  <div class="d-flex flex-column">
                    <div class="dropdown d-flex justify-content-between">
                      <label :for="'placeholder_id_' + index">Placeholder {{ index }}</label>
                      <button
                        @click.prevent="tokenModalHandler(`placeholder${index}`)"
                        class="btn tokenButton dropdown-toggle"
                        type="button"
                        aria-expanded="true"
                      >
                        Contact Tokens
                      </button>
                      <ul class="token-dropdown" v-if="activeTokenModal === `placeholder${index}`">
                        <li>
                          <input
                            @keyup="searchProperties($event)"
                            @keydown.enter.prevent
                            type="text"
                            class="form-control search-input"
                            placeholder="Search property name"
                          />
                        </li>
                        <div class="dropdown-data">
                          <li
                            v-for="property in properties"
                            :key="property.name"
                            :data-name="property.label"
                            class="tokenItem"
                          >
                            <button
                              @click="addToken(property.name, 'placeholder_' + index)"
                              :title="property.name"
                              class="dropdown-item"
                              type="button"
                            >
                              {{ property.label }}
                            </button>
                          </li>
                          <p class="no-data-found">Contact token not found</p>
                        </div>
                      </ul>
                    </div>
                    <input
                      :id="'placeholder_id_' + index"
                      :name="'placeholder_' + index"
                      @keydown.enter.prevent
                      v-model="templateFields[`placeholder_${index}`]"
                      type="text"
                      class="form-control"
                      placeholder="Enter placeholder value "
                      required
                    />
                  </div>
                </div>
              </template>

              <!-- Dynamic data template -->
              <template v-if="matchedDependency">
                <template v-for="(field, index) in matchedDependency.dependentFieldNames">
                  <div class="form-group" :key="index">
                    <div class="d-flex justify-content-between align-items-center">
                      <div class="info-icon-div">
                        <label class="form-label">{{ Object.values(field)[0] }}</label>
                        <div v-if="Object.keys(field)[0].endsWith('_url')" class="info-hint">
                          <img :src="infoIcon" alt="info Icon">
                          <p>Please refer to the Meta guidelines <a href="https://developers.facebook.com/docs/whatsapp/cloud-api/reference/media/#supported-media-types" target="_blank">here</a> in respect of supported media type and  maximum file size</p>
                        </div>
                      </div>
                      <div class="dropdown">
                        <button
                          @click.prevent="tokenModalHandler(Object.values(field)[0])"
                          class="btn tokenButton dropdown-toggle"
                          type="button"
                          data-bs-toggle="dropdown"
                          aria-expanded="false"
                        >
                          Contact Tokens
                        </button>
                        <ul class="token-dropdown" v-if="activeTokenModal === Object.values(field)[0]">
                          <li>
                            <input
                              @keyup="searchProperties($event)"
                              @keydown.enter.prevent
                              type="text"
                              class="form-control search-input"
                              placeholder="Search property name"
                            />
                          </li>
                          <div class="dropdown-data">
                            <li
                              v-for="property in properties"
                              :key="property.name"
                              :data-name="property.label"
                              class="tokenItem"
                            >
                              <button
                                @click="addToken(property.name, Object.keys(field)[0])"
                                :title="property.label"
                                class="dropdown-item"
                                type="button"
                              >
                                {{ property.label }}
                              </button>
                            </li>
                            <p class="no-data-found">Contact token not found</p>
                          </div>
                        </ul>
                      </div>
                    </div>

                    <input :name="Object.keys(field)[0]"
                      :value="tokenValue || defaultUrl || ''"
                      @input="updateField($event, Object.keys(field)[0])" 
                      type="text" 
                      class="form-control"
                      placeholder="Enter value"
                      required />
                  </div>
                </template>
              </template>
            </div>
          </div>
          <div class="modal-footer footer">
            <button :disabled="this.apiLoader" type="submit" class="btn btn-primary">Send</button>
            <Loader v-if="this.apiLoader" />
          </div>
        </form>
      </div>
    </div>
    <error-modal :show="this.sendtemplateError" :toggle="hideErrorModal" :error-message="this.errorMessage" />
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import axios from '@/utils/api.js'
import Loader from '../Loader/Loader.vue'
import templateData from './template.json'
import closeIcon from '../../../assets/icons/close-icon.svg'
import infoIcon from '../../../assets/icons/info.svg'
import ErrorModal from '../ErrorModal/ErrorModal'

export default {
  name: 'TemplateModal',
  emits: ['create-handler'],
  components: {
    Loader,
    ErrorModal
  },
  props: {
    selectedTemplate: Object,
    roomPhone: String,
    tokens: String,
    objectId: Number,
    defaultHeaderTemplateUrl: String,
  },

  data() {
    return {
      properties: [], // Holds the list of hubspot properties,
      templateFields: {},
      activeTokenModal: null,
      apiLoader: false,
      closeIcon: closeIcon,
      infoIcon: infoIcon,
      sendtemplateError: false,
      errorMessage: 'Failed to send template, Please try again',
      tokenValue: null,
      defaultUrl: null,
    }
  },

  computed: {
    matchedDependency() {
      return templateData.find(dep => dep.controllingFieldValue === this.selectedTemplate?.type)
    }
  },

  watch: {
    defaultHeaderTemplateUrl: {
      handler(newVal) {
        if (newVal) {
          let dependancyKey = templateData.find(dep => dep.controllingFieldValue === this.selectedTemplate?.type)
          if (!dependancyKey || !dependancyKey.dependentFieldNames || dependancyKey.dependentFieldNames.length === 0) {
            return;
          }
          let objectKey = Object.keys(dependancyKey?.dependentFieldNames[0])[0]
          this.$set(this.templateFields, objectKey, newVal);
          this.defaultUrl = newVal;
        }
      },
      immediate: true,
      deep: true,
    }
  },

  created() {
    const userData = this.$store.state.userData
    this.user_id = userData.user_id
    this.getHubspotProperties()
  },

  methods: {
    ...mapMutations(['setNewMessage']),

    updateField(event, key) {
      const decodedValue = decodeURIComponent(event?.target?.value || '');
      this.$set(this.templateFields, key, decodedValue);
      this.tokenValue = decodedValue;
      this.defaultUrl = null;
    },

    hideErrorModal() {
      this.sendtemplateError = false
    },

    // Token modal handler
    tokenModalHandler(selectedData) {
      if (this.activeTokenModal === selectedData) {
        this.activeTokenModal = null
      } else {
        this.activeTokenModal = selectedData
      }
    },

    closeModal() {
      this.tokenValue = null
      this.$emit('close-modal')
      this.templateFields = {}
      this.tokenModalHandler(null)
    },

    searchProperties(event) {
      let searchValue = event.target.value.trim().toLowerCase()
      let tokenItems = event.target.parentElement.parentElement.parentElement.querySelectorAll('.tokenItem')
      let notfound = document.querySelector('.no-data-found')
      let found = false // Track if any tokens are found

      if (tokenItems) {
        for (let i = 0; i < tokenItems.length; i++) {
          let token = tokenItems[i]
          let tokenName = token.getAttribute('data-name').trim().toLowerCase()

          if (tokenName.includes(searchValue)) {
            token.style.display = 'block'
            found = true // A match is found
          } else {
            token.style.display = 'none'
          }
        }
      }

      // Display "no data found" message if no tokens are found
      if (!found) {
        notfound.style.display = 'flex'
      } else {
        notfound.style.display = 'none'
      }
    },

    // Select token from dropdown list

    addToken(token, target) {
    if (!this.templateFields[target]) {
      this.$set(this.templateFields, target, ""); // Initialize empty if not set
    }         
    this.tokenValue = `[${token}]`
    this.templateFields[target] = `[${token}]`;
    this.defaultUrl = null;
    this.activeTokenModal = null;
  },

    submitHandler(event) {
      let data = {
        phone: this.roomPhone,
        objectId: this.objectId,
        messageData: this.selectedTemplate,
        message: this.selectedTemplate.body,
        templateId: this.selectedTemplate.id,
        fields: this.templateFields
      }

      const reqData = {
        user_id: this.user_id,
        ...data
      }
      this.apiLoader = true
      try {
        axios
          .post(`api/v1/send-template`, reqData)
          .then(response => {
            this.apiLoader = false
            if (response.data.ok) {
              this.closeModal()
              this.setNewMessage(response.data.message)
            } else {
              this.errorMessage = response.data.message
              this.sendtemplateError = true
            }
          })
          .catch(error => {
            this.apiLoader = false
            this.sendtemplateError = true
            console.error(error)
          })
      } catch (err) {
        this.apiLoader = false
        console.log(err)
      }
    },

    // Fetch hubspot properties
    async getHubspotProperties() {
      try {
        const response = await axios.get(`api/hubspot/properties?user_id=${this.user_id}`)
        this.properties = response.data.data.results
      } catch (error) {
        console.error(error)
      }
    }
  }
}
</script>
