{"name": "chat-app", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode dev", "build:staging": "vue-cli-service build --mode staging", "lint": "vue-cli-service lint"}, "dependencies": {"@popperjs/core": "^2.11.7", "axios": "^0.24.0", "bootstrap": "^5.3.3", "chart.js": "^4.4.1", "core-js": "^3.6.5", "emoji-picker-element": "^1.15.1", "floating-vue": "^1.0.0-beta.19", "iso-639-1": "^3.1.5", "jquery": "^3.7.0", "linkifyjs": "^4.1.0", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "pusher-js": "^7.0.3", "v-click-outside": "^3.2.0", "vue": "^2.6.11", "vue-color": "^2.8.1", "vue-date-pick": "^1.5.1", "vue-funnel-graph-js": "https://github.com/pravnyadv/vue-funnel-graph-js/tarball/master", "vue-infinite-loading": "https://github.com/antoine92190/vue-infinite-loading/tarball/master", "vue-loader": "^17.0.1", "vue-router": "^3.2.0", "vue-select": "^3.20.2", "vue-simple-search-dropdown": "^1.0.1", "vue-text-highlight": "^2.0.10", "vue2-daterange-picker": "^0.6.8", "vuex": "^3.4.0"}, "devDependencies": {"@babel/eslint-parser": "^7.19.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "^5.0.8", "del-cli": "^5.0.0", "eslint": "^8.32.0", "eslint-plugin-vue": "^9.9.0", "sass": "1.77.6", "sass-loader": "^13.2.0", "vue-template-compiler": "^2.6.11"}, "optionalDependencies": {"lamejs": "^1.2.1"}, "engines": {"node": ">=18.20.0"}}