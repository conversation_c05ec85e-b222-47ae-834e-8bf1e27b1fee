import axios from 'axios'
import config from './config'

// Create axios instance
const apiClient = axios.create({
  baseURL: config.baseURL,
  headers: {
    'TimeZoneOffset': new Date().getTimezoneOffset(),
  }
})

// Request interceptor to add JWT token to all requests
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('jwt_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token expiration
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      // Token expired or invalid, remove it
      localStorage.removeItem('jwt_token')
      // Optionally redirect to login or refresh page
      console.warn('JWT token expired or invalid')
    }
    return Promise.reject(error)
  }
)

// Helper function to set JWT token
export const setJwtToken = (token) => {
  if (token) {
    localStorage.setItem('jwt_token', token)
  }
}

// Helper function to remove JWT token
export const removeJwtToken = () => {
  localStorage.removeItem('jwt_token')
}

// Helper function to get JWT token
export const getJwtToken = () => {
  return localStorage.getItem('jwt_token')
}

export default apiClient
