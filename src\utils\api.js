import axios from 'axios'
import config from './config'

// Create axios instance
const apiClient = axios.create({
  baseURL: config.baseURL,
  headers: {
    'TimeZoneOffset': new Date().getTimezoneOffset(),
  }
})

// Request interceptor to add JWT token to all requests
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('jwt_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    } else {
      delete config.headers.Authorization
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.log('API Error:', error.response?.data || error.message)
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('jwt_token')
      console.warn('JWT token expired or invalid')
    }
    return Promise.reject(error)
  }
)

export const setJwtToken = (token) => {
  if (token) {
    localStorage.setItem('jwt_token', token)
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`
  }
}

export const removeJwtToken = () => {
  localStorage.removeItem('jwt_token')
  delete apiClient.defaults.headers.common['Authorization']
}

export const getJwtToken = () => {
  return localStorage.getItem('jwt_token')
}

const existingToken = localStorage.getItem('jwt_token')
if (existingToken) {
  apiClient.defaults.headers.common['Authorization'] = `Bearer ${existingToken}`
}

export default apiClient
